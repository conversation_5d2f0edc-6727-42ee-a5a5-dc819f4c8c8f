<template>
  <router-view />
</template>

<style lang="scss">
@font-face {
  font-family: 'Minecraft-EN';
  src: url('@/assets/minecraft-font.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  unicode-range: U+0020-007F; /* 基本拉丁字符，包括英文字母和基本符号，但排除数字 */
}

@font-face {
  font-family: 'Minecraft-CN';
  src: url('@/assets/minecraft-font-cn.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+F900-FAFF, U+2F800-2FA1F; /* 完整的中文字符范围 */
}

/* 为数字和特殊字符使用系统字体 */
@font-face {
  font-family: 'Minecraft-Numbers';
  src: local('Consolas'), local('Monaco'), local('Courier New');
  unicode-range: U+0030-0039, U+002E, U+002C, U+0025; /* 数字0-9，点，逗号，百分号 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden; /* 阻止页面滚动 */
}

body {
  font-family: 'Minecraft-Numbers', 'Minecraft-CN', 'Minecraft-EN', 'Consolas', 'Monaco', 'Courier New', sans-serif;
  -webkit-font-smoothing: none;
  -moz-osx-font-smoothing: grayscale;
  color: #ffffff;
  background-color: #111111;
  line-height: 1.6;
}

#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

button, input, textarea {
  font-family: 'Minecraft-Numbers', 'Minecraft-CN', 'Minecraft-EN', 'Consolas', 'Monaco', 'Courier New', sans-serif;
  -webkit-font-smoothing: none;
}
</style>