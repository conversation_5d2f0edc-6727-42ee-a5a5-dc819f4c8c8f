<template>
  <router-view />
</template>

<style lang="scss">
@font-face {
  font-family: 'Minecraft';
  src: url('@/assets/minecraft-font.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Minecraft';
  src: url('@/assets/minecraft-font-cn.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+F900-FAFF, U+2F800-2FA1F; /* 完整的中文字符范围 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden; /* 阻止页面滚动 */
}

body {
  font-family: 'Minecraft', sans-serif;
  -webkit-font-smoothing: none;
  -moz-osx-font-smoothing: grayscale;
  color: #ffffff;
  background-color: #111111;
  line-height: 1.6;
}

#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

button, input, textarea {
  font-family: 'Minecraft', sans-serif;
  -webkit-font-smoothing: none;
}
</style>