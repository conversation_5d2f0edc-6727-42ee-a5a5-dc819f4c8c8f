<template>
  <div class="chat-view">
    <!-- 分屏或单屏状态下收起侧栏时的展开按钮，放在主内容顶部标题栏内 -->
    <div v-if="sidebarCollapsed" class="header-float-btn-bar">
      <button class="sidebar-float-open-btn" @click="toggleSidebar" title="展开侧边栏">
        <span class="material-icons">menu</span>
      </button>
    </div>
    <!-- 侧边栏浮层和主内容蒙层（分屏时） -->
    <div v-if="isSplitPane && !sidebarCollapsed" class="sidebar-float-layer">
      <div class="sidebar sidebar-float">
        <!-- 侧边栏内容复用原有结构 -->
        <div class="sidebar-header">
          <button class="new-chat-btn" @click="createNewChat">
            <span class="material-icons">add</span>
            新建对话
          </button>
          <button class="toggle-sidebar-btn" @click="toggleSidebar">
            <span class="material-icons">chevron_left</span>
          </button>
        </div>        <div class="conversations-list">
          <div v-if="loadingConversations" class="loading-conversations">
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div>加载中...</div>
          </div>
          <div v-else-if="conversations.length === 0" class="no-conversations">
            <span class="material-icons">chat</span>
            <p>暂无会话记录</p>
            <p class="help-text">点击"新建对话"开始聊天</p>
          </div>
          <div 
            v-else
            v-for="conversation in conversations" 
            :key="conversation.id"
            class="conversation-item"
            :class="{ 'active': currentConversationId === conversation.id }"
            @click="selectConversation(conversation.id)"
          >
            <div class="conversation-info">
              <div class="conversation-title">
                <span class="mode-icon">{{ conversation.mode === 'crafting' ? '🔧' : '💬' }}</span>
                {{ conversation.title || '新对话' }}
              </div>
              <div class="conversation-date">{{ formatDate(conversation.updated_at) }}</div>
            </div>
            <div class="conversation-actions">
              <button 
                class="edit-btn" 
                @click.stop="startEditingTitle(conversation)"
                title="编辑标题"
              >
                <span class="material-icons">edit</span>
              </button>
              <button 
                class="delete-btn" 
                @click.stop="confirmDeleteConversation(conversation.id)"
                title="删除对话"
              >
                <span class="material-icons">delete</span>
              </button>
            </div>
          </div>
        </div>
        <div class="sidebar-bottom">
          <button v-if="isAdmin" class="sidebar-btn home-btn" @click="router.push('/admin')">
            管理后台
          </button>
        </div>
      </div>
      <div class="main-float-mask" @click="toggleSidebar"></div>
    </div>
    <!-- 侧边栏常规模式（单屏时） -->
    <div v-if="!isSplitPane && !sidebarCollapsed" class="sidebar">
      <!-- 侧边栏内容复用原有结构 -->
      <div class="sidebar-header">
        <button class="new-chat-btn" @click="createNewChat">
          <span class="material-icons">add</span>
          新建对话
        </button>
        <button class="toggle-sidebar-btn" @click="toggleSidebar">
          <span class="material-icons">chevron_left</span>
        </button>
      </div>      <div class="conversations-list">
        <div v-if="loadingConversations" class="loading-conversations">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div>加载中...</div>
        </div>
        <div v-else-if="conversations.length === 0" class="no-conversations">
          <span class="material-icons">chat</span>
          <p>暂无会话记录</p>
          <p class="help-text">点击"新建对话"开始聊天</p>
        </div>
        <div 
          v-else
          v-for="conversation in conversations" 
          :key="conversation.id"
          class="conversation-item"
          :class="{ 'active': currentConversationId === conversation.id }"
          @click="selectConversation(conversation.id)"
        >
          <div class="conversation-info">
            <div class="conversation-title">
              <span class="mode-icon">{{ conversation.mode === 'crafting' ? '🔧' : '💬' }}</span>
              {{ conversation.title || '新对话' }}
            </div>
            <div class="conversation-date">{{ formatDate(conversation.updated_at) }}</div>
          </div>
          <div class="conversation-actions">
            <button 
              class="edit-btn" 
              @click.stop="startEditingTitle(conversation)"
              title="编辑标题"
            >
              <span class="material-icons">edit</span>
            </button>
            <button 
              class="delete-btn" 
              @click.stop="confirmDeleteConversation(conversation.id)"
              title="删除对话"
            >
              <span class="material-icons">delete</span>
            </button>
          </div>
        </div>
      </div>
      <div class="sidebar-bottom">
        <button v-if="isAdmin" class="sidebar-btn home-btn" @click="router.push('/admin')">
          管理后台
        </button>
      </div>
    </div>    <!-- 分屏主内容 -->
    <SplitPane v-if="isSplitPane" :defaultLeft="400" persistKey="chat-crafting">
      <template #left>
        <div class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
          <ChatInterface ref="chatInterface" :conversation-id="currentConversationId" :isSplitPane="isSplitPane" @new-chat="createNewChat" @trigger-crafting-split="onTriggerCraftingSplit" />
        </div>
      </template>
      <template #right>
        <CraftingTree
          :initialTree="currentCraftingTree"
          @close="isSplitPane = false"
          @insert="itemName => { console.log('[ChatView] tree insert', itemName); chatInterface?.insertItemNameToChatInput(itemName) }"
        />
      </template>
    </SplitPane>
    <!-- 单屏主内容 -->
    <div v-else class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <ChatInterface ref="chatInterface" :conversation-id="currentConversationId" :isSplitPane="isSplitPane" @new-chat="createNewChat" @trigger-crafting-split="onTriggerCraftingSplit" />
    </div>

    <!-- 编辑会话标题对话框 -->
    <div class="modal" v-if="showEditTitleModal">
      <div class="modal-content">
        <h3>编辑会话标题</h3>
        <input 
          type="text" 
          v-model="editingTitle" 
          placeholder="请输入新标题"
          class="title-input"
          @keydown.enter="saveTitle"
          ref="titleInput"
        />
        <div class="modal-actions">
          <button class="cancel-btn" @click="cancelEditTitle">取消</button>
          <button class="save-btn" @click="saveTitle">保存</button>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div class="modal" v-if="showDeleteModal">
      <div class="modal-content">
        <h3>删除会话</h3>
        <p>确定要删除这个会话吗？此操作不可恢复。</p>
        <div class="modal-actions">
          <button class="cancel-btn" @click="cancelDelete">取消</button>
          <button class="delete-confirm-btn" @click="confirmDelete">删除</button>
        </div>      </div>
    </div>

    <!-- 模式选择弹窗 -->
    <ModeSelectionModal 
      :isVisible="showModeSelectionModal" 
      @close="store.dispatch('hideModeSelectionModal')" 
      @selectMode="handleModeSelection" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import ChatInterface from '@/components/chat/ChatInterface.vue';
import SplitPane from '@/components/common/SplitPane.vue';
import CraftingTree from '@/components/chat/CraftingTree.vue';
import ModeSelectionModal from '@/components/chat/ModeSelectionModal.vue';
import { Message } from '@/types';
import type { CraftingTreeNodeData } from '@/types';

const store = useStore();
const router = useRouter();
const isAdmin = computed(() => store.getters.isAdmin);
const chatInterface = ref<InstanceType<typeof ChatInterface> | null>(null);
const isSplitPane = ref(false);
const currentConversationId = computed(() => {
  const id = store.state.currentConversation?.id || null;
  return isTempId(id) ? null : id;
});
// 完整合成树数据由消息携带
const currentCraftingTree = ref<CraftingTreeNodeData | null>(null);

// 侧边栏状态
const sidebarCollapsed = ref(false);

// 模式选择弹窗状态
const showModeSelectionModal = computed(() => store.state.showModeSelectionModal);

// 检查是否为临时ID（以temp_开头）
const isTempId = (id: any): boolean => {
  return typeof id === 'string' && id.toString().startsWith('temp_');
};

// 会话列表
const conversations = computed(() => store.state.conversations || []);
const loadingConversations = computed(() => store.state.loadingConversations);

// 编辑标题相关状态
const showEditTitleModal = ref(false);
const editingTitle = ref('');
const editingConversationId = ref<number | null>(null);
const titleInput = ref<HTMLInputElement | null>(null);

// 删除对话相关状态
const showDeleteModal = ref(false);
const deletingConversationId = ref<number | null>(null);

// 格式化日期显示
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr);
  const now = new Date();
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  if (diffDays === 0) {
    return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  } else if (diffDays === 1) {
    return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }
};

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

// 选择会话
const selectConversation = async (id: number) => {
  await store.dispatch('fetchConversationDetail', id);
};

// 创建新会话
const createNewChat = () => {
  // 显示模式选择弹窗
  store.dispatch('showModeSelectionModal');
};

// 处理模式选择
const handleModeSelection = (mode: 'normal' | 'crafting') => {
  store.dispatch('createConversationWithMode', { mode });
};

// 开始编辑标题
const startEditingTitle = (conversation: any) => {
  editingConversationId.value = conversation.id;
  editingTitle.value = conversation.title;
  showEditTitleModal.value = true;
  nextTick(() => {
    if (titleInput.value) {
      titleInput.value.focus();
      titleInput.value.select();
    }
  });
};

// 保存标题
const saveTitle = async () => {
  if (editingConversationId.value && editingTitle.value.trim()) {
    await store.dispatch('updateConversationTitle', {
      conversationId: editingConversationId.value,
      title: editingTitle.value.trim()
    });
    showEditTitleModal.value = false;
    editingConversationId.value = null;
    editingTitle.value = '';
  }
};

// 取消编辑标题
const cancelEditTitle = () => {
  showEditTitleModal.value = false;
  editingConversationId.value = null;
  editingTitle.value = '';
};

// 确认删除会话
const confirmDeleteConversation = (id: number) => {
  deletingConversationId.value = id;
  showDeleteModal.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (deletingConversationId.value) {
    await store.dispatch('deleteConversation', deletingConversationId.value);
    if (deletingConversationId.value === currentConversationId.value) {
      createNewChat();
    }
    showDeleteModal.value = false;
    deletingConversationId.value = null;
  }
};

// 取消删除
const cancelDelete = () => {
  showDeleteModal.value = false;
  deletingConversationId.value = null;
};

// 合成树相关方法
function onTriggerCraftingSplit(msg: any) {
  console.log('[ChatView] onTriggerCraftingSplit called with:', msg);
  
  if (!msg) {
    console.warn('[ChatView] No message data provided to onTriggerCraftingSplit');
    return;
  }
  
  // 统一处理不同格式的消息
  try {
    // 场景1：消息本身包含craftingData属性
    if (msg.craftingData) {
      console.log('[ChatView] Setting crafting tree from message.craftingData');
      currentCraftingTree.value = msg.craftingData;
    }
    // 场景2：消息本身就是合成树数据
    else if (msg.itemName && msg.recipe) {
      console.log('[ChatView] Message appears to be a crafting tree itself');
      currentCraftingTree.value = msg;
    }
    // 场景3：从SSE回调收到的newCtx.sharedTree
    else if (msg.sharedTree) {
      console.log('[ChatView] Setting crafting tree from message.sharedTree');
      currentCraftingTree.value = msg.sharedTree;
    }
    else {
      console.warn('[ChatView] Message does not contain valid crafting data:', msg);
      return;
    }
    
    // 直接打开分屏，不等待watcher触发
    console.log('[ChatView] Opening split pane');
    sidebarCollapsed.value = true;
    isSplitPane.value = true;
  } catch (error) {
    console.error('[ChatView] Error processing crafting message:', error);
  }
}

// 保留watcher作为后备机制
watch(currentCraftingTree, (tree) => {
  if (tree) {
    console.log('[ChatView] Crafting tree updated, ensuring split pane is open');
    sidebarCollapsed.value = true;
    isSplitPane.value = true;
  }
});

// 组件挂载时加载会话列表
onMounted(async () => {
  await store.dispatch('fetchConversations');
});
</script>

<style lang="scss" scoped>
.chat-view {
  height: 100%;
  background-image: url('@/assets/minecraft-background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

// 侧边栏样式
.sidebar {
  width: 240px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  border-right: 2px solid #5e5e5e;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  flex-shrink: 0;

  &.collapsed {
    width: 50px;
  }
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 2px solid #5e5e5e;
}

.new-chat-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #55ff55;
  color: #000000;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Minecraft', sans-serif;

  &:hover {
    background-color: #7dff7d;
    transform: translateY(-1px);
  }

  .material-icons {
    margin-right: 5px;
    font-size: 5px;
  }
}

.toggle-sidebar-btn {
  margin-left: 10px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #aaaaaa;
  cursor: pointer;

  &:hover {
    color: #ffffff;
  }
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.loading-conversations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #aaaaaa;
}

.no-conversations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #aaaaaa;

  .material-icons {
    font-size: 36px;
    margin-bottom: 10px;
  }

  p {
    margin: 5px 0;
  }

  .help-text {
    font-size: 12px;
    margin-top: 10px;
    opacity: 0.7;
  }
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 5px;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.active {
    background-color: rgba(85, 255, 85, 0.2);
    border-left: 3px solid #55ff55;
  }

  .conversation-info {
    flex: 1;
    overflow: hidden;
  }
  .conversation-title {
    font-size: 14px;
    color: #ffffff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .mode-icon {
    font-size: 12px;
    flex-shrink: 0;
    opacity: 0.8;
  }

  .conversation-date {
    font-size: 12px;
    color: #aaaaaa;
  }

  .conversation-actions {
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity 0.2s;

    button {
      background: none;
      border: none;
      color: #aaaaaa;
      cursor: pointer;
      padding: 3px;
      margin-left: 5px;
      border-radius: 3px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      .material-icons {
        font-size: 16px;
      }
    }

    .delete-btn:hover {
      color: #ff5555;
    }
  }

  &:hover .conversation-actions {
    opacity: 1;
  }
}

// 右侧主内容
.main-content {
  flex: 1;
  height: 100%;
  display: flex;
  transition: margin-left 0.3s ease;

  &.sidebar-collapsed {
    margin-left: 0;
  }
}

// 分屏右侧区域
.split-right-pane {
  height: 100%;
  background: rgba(30,40,30,0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.split-cancel-btn {
  position: absolute;
  top: 18px;
  left: 18px;
  z-index: 10;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(30,30,30,0.92);
  color: #fff;
  border: none;
  box-shadow: 0 2px 8px rgba(0,0,0,0.13);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  font-size: 20px;
  outline: none;

  &:hover {
    background: #232c23;
    color: #ff5555;
  }
}

.crafting-tree-placeholder {
  color: #55ff55;
  font-size: 22px;
  font-family: 'Minecraft', monospace;
  opacity: 0.7;
}

// 模态框样式
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.2s ease;
}

.modal-content {
  background-color: rgba(20, 20, 20, 0.95);
  border: 2px solid #5e5e5e;
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s ease;

  h3 {
    color: #55ff55;
    margin-bottom: 15px;
    font-family: 'Minecraft', monospace;
    text-align: center;
  }

  p {
    color: #ffffff;
    margin-bottom: 20px;
    text-align: center;
  }

  .title-input {
    width: 100%;
    height: 40px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 2px solid #5e5e5e;
    border-radius: 4px;
    color: #ffffff;
    font-family: 'Minecraft', sans-serif;
    margin-bottom: 20px;

    &:focus {
      outline: none;
      border-color: #55ff55;
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;

    button {
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      font-family: 'Minecraft', sans-serif;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        transform: translateY(-1px);
      }
    }

    .cancel-btn {
      background-color: #444444;
      color: #ffffff;

      &:hover {
        background-color: #555555;
      }
    }

    .save-btn {
      background-color: #55ff55;
      color: #000000;

      &:hover {
        background-color: #7dff7d;
      }
    }

    .delete-confirm-btn {
      background-color: #ff5555;
      color: #ffffff;

      &:hover {
        background-color: #ff7777;
      }
    }
  }
}

// 加载点
.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  margin: 0 3px;
  background-color: #aaaaaa;
  border-radius: 50%;
  display: inline-block;
  animation: loading-dot 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-dot {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式调整
@media (max-width: 768px) {
  .sidebar:not(.collapsed) {
    width: 240px;
  }

  .sidebar.collapsed {
    width: 0;
    border: none;
  }
}

.sidebar-bottom {
  margin-top: auto;
  padding: 24px 0 24px 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.sidebar-btn {
  background: #2c2c2c;
  color: #55ff55;
  border: 2px solid #55ff55;
  font-family: 'Minecraft', monospace;
  font-size: 16px;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  cursor: pointer;
  transition: background 0.2s, color 0.2s, transform 0.2s;
}

.home-btn {
  margin: 0 16px;
}

.sidebar-btn:hover {
  background: #55ff55;
  color: #2c2c2c;
  transform: translateY(-2px);
}

// 侧边栏浮层样式
.sidebar-float-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2000;
  pointer-events: none;
}

.sidebar-float {
  position: absolute;
  top: 0;
  left: 0;
  width: 260px;
  height: 100vh;
  background: rgba(30,30,30,0.98);
  border-radius: 0 18px 18px 0;
  box-shadow: 0 4px 32px 0 rgba(0,0,0,0.25);
  z-index: 2100;
  pointer-events: all;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-float-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(40,40,40,0.22);
  z-index: 2050;
  pointer-events: all;
  transition: background 0.2s;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
}

.header-float-btn-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 60px;
  z-index: 1200;
  display: flex;
  align-items: center;
  pointer-events: none;
}

.sidebar-float-open-btn {
  margin-left: 18px;
  margin-top: 12px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: none;
  color: #aaaaaa;
  border: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  font-size: 18px;
  outline: none;
  pointer-events: all;
}

.sidebar-float-open-btn:hover {
  background: rgba(255,255,255,0.08);
  color: #ffffff;
}

.crafting-card-placeholder {
  margin: 32px auto 0 auto;
  display: flex;
  justify-content: center;
}
.crafting-card {
  display: flex;
  align-items: center;
  background: rgba(30,40,30,0.92);
  border: 2px solid #55ff55;
  border-radius: 12px;
  box-shadow: 0 2px 12px #55ff5555;
  padding: 18px 28px;
  cursor: pointer;
  transition: box-shadow 0.18s, border 0.18s;
  min-width: 320px;
  max-width: 480px;
  margin: 0 auto;
  &:hover {
    box-shadow: 0 4px 24px #55ff55cc;
    border-color: #7dff7d;
  }
}
.crafting-card-icon {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  margin-right: 18px;
  background: #232;
  object-fit: contain;
}
.crafting-card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.crafting-card-title {
  font-size: 18px;
  color: #55ff55;
  font-family: 'Minecraft', monospace;
  font-weight: bold;
}
.crafting-card-desc {
  font-size: 14px;
  color: #fff;
  opacity: 0.85;
  margin-bottom: 6px;
}
.crafting-card-btn {
  background: #55ff55;
  color: #232;
  border: none;
  border-radius: 6px;
  padding: 4px 16px;
  font-family: 'Minecraft', monospace;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.18s, color 0.18s;
  &:hover {
    background: #7dff7d;
    color: #111;
  }
}
</style>