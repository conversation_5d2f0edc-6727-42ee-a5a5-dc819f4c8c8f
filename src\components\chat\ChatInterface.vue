<template>
  <div class="chat-container">
    <!-- Minecraft风格标题 -->
    <div class="chat-header">
      <div class="header-left"></div> <!-- 左侧空白占位 -->
      <div class="header-center">
        <h1>Minecraft 知识助手</h1>
        <button class="feedback-button" @click="showFeedbackModal = true" title="提交反馈">
          <span class="feedback-icon"></span>
          <span class="feedback-text">反馈</span>
        </button>
      </div>
      <div class="user-info" v-if="currentUser">
        <span class="username">{{ currentUser.username }}</span>
        <button class="logout-button" @click="logout" title="退出登录">
          <span class="material-icons">logout</span>
        </button>
      </div>
    </div>

    <!-- 消息显示区域 -->
    <div class="messages-container" ref="messagesContainer">
      <!-- 没有对话时显示的欢迎消息 -->
      <div v-if="!currentConversation || !currentConversation.messages || currentConversation.messages.length === 0"
        class="welcome-message">
        <div class="minecraft-icon creeper"></div>
        <h2>欢迎使用Minecraft助手！</h2>
        <p v-if="currentUser">欢迎回来，{{ currentUser.username }}！</p>
        <p>你可以向我询问关于Minecraft的任何问题 - 合成配方、生物、生态群系等等！</p>
      </div>

      <MessageComponent v-for="(message, index) in currentConversation?.messages || []" :key="index" :message="message"
        :isSplitPane="props.isSplitPane" @expand-split="onExpandSplit" @select-item="insertItemNameToChatInput">
        <template #loading>
          <!-- Minecraft风格的加载动画 -->
          <div class="minecraft-loader">
            <div class="cube-container">
              <div class="cube">
                <div class="face front"></div>
                <div class="face back"></div>
                <div class="face right"></div>
                <div class="face left"></div>
                <div class="face top"></div>
                <div class="face bottom"></div>
              </div>
            </div>
          </div>
          <!-- 加载状态文字 -->
          <div class="loading-status">{{ loadingStatusText }}</div>
          <!-- Minecraft小知识提示 -->
          <div class="minecraft-tip">
            <div class="tip-header">
              <span class="tip-icon"></span>
              <span>你知道吗？</span>
            </div>
            <div class="tip-content">{{ currentLoadingTip }}</div>
          </div>
          <!-- 取消按钮 -->
          <div class="cancel-container">
            <button @click="cancelStream" class="cancel-button">
              <span class="cancel-icon"></span>
              <span>停止生成</span>
            </button>
          </div>
        </template>
      </MessageComponent>
    </div> <!-- 用户输入区域 -->
    <div class="input-bar">
      <div class="input-row">
        <div class="input-affix-wrapper"> <span
            v-if="isConversationCraftingMode || store.state.nextConversationMode === 'SYNTHETIC_NAVIGATION'"
            class="input-mode-affix">
            <span class="mode-tag-text">合成导航</span>
          </span> <textarea v-model="userInput" @keydown.enter.prevent="sendMessage" @input="autoResizeTextarea"
            :placeholder="dynamicPlaceholder" class="user-input" ref="inputField" :disabled="isLoadingResponse"
            :class="{ 'input-crafting-mode': isConversationCraftingMode || store.state.nextConversationMode === 'SYNTHETIC_NAVIGATION' }"
            :style="(isConversationCraftingMode || store.state.nextConversationMode === 'SYNTHETIC_NAVIGATION') ? 'padding-left: 106px;' : ''"></textarea>
        </div> <!-- 查看合成树按钮 -->
        <button v-if="showReenterSplitButton" @click="reenterSplitPane" class="tree-button"
          :disabled="isLoadingResponse" title="查看合成树">
          <span class="minecraft-icon crafting-table"></span>
        </button>
        <button @click="sendMessage" class="send-button" :disabled="isLoadingResponse || !userInput.trim()">
          <span class="minecraft-icon arrow"></span>
        </button>
      </div>
    </div>

    <!-- 反馈弹窗组件 -->
    <FeedbackModal :isVisible="showFeedbackModal" @close="showFeedbackModal = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { Message } from '@/types';
import FeedbackModal from './FeedbackModal.vue';
import MessageComponent from './Message.vue';
import CraftingRecipeCard from './CraftingRecipeCard.vue'; // 确保导入
import { startNewConversationSSE, addMessageToConversationSSE } from '@/services/conversationSSE';

const props = defineProps<{
  conversationId: number | string | null,
  isSplitPane?: boolean
}>();
const emit = defineEmits(['new-chat', 'trigger-crafting-split']);

const store = useStore();
const router = useRouter();
const messages = ref<Message[]>([]);
const userInput = ref('');
const messagesContainer = ref<HTMLElement | null>(null);
const inputField = ref<HTMLTextAreaElement | null>(null);
const loadingStatusText = ref('研究中，请稍候...');
const currentLoadingTip = ref('');
const tipIndex = ref(0);
const loadingStage = ref(0);
const loadingStartTime = ref(0);
const showFeedbackModal = ref(false);

// 方法：将物品名称插入到聊天输入框
function insertItemNameToChatInput(itemName: string) {
  const currentValue = userInput.value;
  if (currentValue.length > 0 && !currentValue.endsWith(' ')) {
    userInput.value += ' '; // 如果输入框非空且末尾不是空格，则加一个空格
  }
  userInput.value += itemName; // 追加物品名称

  // 聚焦到输入框
  inputField.value?.focus();
}

// Expose insertion method for parent via ref
defineExpose({ insertItemNameToChatInput });

// 重新进入分屏模式
const reenterSplitPane = () => {
  console.log('[ChatInterface] reenterSplitPane called');
  // 查找当前对话中包含合成树的最新消息
  const messages = currentConversation.value?.messages || [];
  const lastCraftingMessage = [...messages].reverse().find(msg =>
    msg.type === 'CRAFTING' && msg.craftingData
  );

  if (lastCraftingMessage) {
    console.log('[ChatInterface] Found crafting message for reenter:', lastCraftingMessage.id);
    // 使用onExpandSplit统一处理消息
    onExpandSplit(lastCraftingMessage);
  } else {
    console.warn('[ChatInterface] No crafting message found for reenter');
  }
};

const currentUser = computed(() => store.state.user);
const currentConversation = computed(() => store.state.currentConversation);

// 根据当前对话的模式决定是否显示合成导航标签
const isConversationCraftingMode = computed(() => {
  return currentConversation.value?.type === 'SYNTHETIC_NAVIGATION';
});

// 动态输入框提示
const dynamicPlaceholder = computed(() => {
  // 检查是否在等待特定模式的输入
  const nextMode = store.state.nextConversationMode;
  if (nextMode === 'SYNTHETIC_NAVIGATION') {
    return '请输入要合成的物品名称...';
  }

  if (!isConversationCraftingMode.value) return '请输入你的Minecraft问题...';
  const ctx = store.state.currentCraftingContext;
  return ctx ? `请继续询问关于 "${ctx.targetItem}" 的合成问题...` : '请输入要合成的物品名称...';
});

// 是否显示重新进入分屏按钮
const showReenterSplitButton = computed(() => {
  return isConversationCraftingMode.value && !props.isSplitPane && !!store.state.currentCraftingContext;
});

const minecraftTips = [
  "苦力怕最初是一个编程错误的结果，史蒂夫的模型被意外拉长了！",
  "末影人害怕南瓜，戴上南瓜头可以直视它们而不被攻击。",
  "僵尸和骷髅在阳光下会燃烧，但在水中或戴头盔时不会。",
  "红石的最大传输距离为15格。",
  "村民的交易和职业由其工作方块决定，如锻造台、制图台等。",
  "猫可以吓跑苦力怕，豹猫则会攻击幻翼。",
  "牛、哞菇和鸡不需要草方块也能存活，但羊需要。",
  "一个西瓜可以制作8片西瓜片。",
  "钻石矿通常在Y坐标11左右最常见。",
  "在基岩版中，河流的水颜色随生态群系而变化。",
  "蜜蜂每个蜂巢可以存储5份蜂蜜。",
  "拥有幸运III的工具最多可以从一个钻石矿获得4个钻石。",
  "凋灵骷髅可以召唤，需要3个凋灵骷髅头和4个灵魂沙。",
  "在下界，水会立即蒸发，但岩浆流动更快。",
  "在下界，指南针会疯狂旋转，钟会显示随机时间。",
  "每个方块的视野范围是前方16格。",
  "唱片\"11\"和\"13\"是游戏中唯一具有故事性的唱片。"
];

const isTempId = (id: any): boolean => {
  return typeof id === 'string' && id.startsWith('temp_');
};

const loadConversationMessages = async () => {
  if (props.conversationId && !isTempId(props.conversationId)) {
    try {
      await store.dispatch('fetchConversationDetail', props.conversationId);
    } catch (error) {
      console.error('获取会话详情失败:', error);
    }
  }
};

watch(() => props.conversationId, (newId, oldId) => {
  if (newId) {
    if (isTempId(newId)) {
      console.log('跳过临时ID的会话加载:', newId);
      return;
    }
    if (newId === oldId) {
      console.log('跳过相同ID的重复加载:', newId);
      return;
    }
    const isLoading = !!currentConversation.value?.messages?.some((m: Message) => m.isLoading);
    if (isLoading) {
      console.log('跳过加载，因为当前有消息正在生成:', newId);
      return;
    }
    console.log('加载会话消息:', newId);
    loadConversationMessages();
  } else if (newId === null && oldId !== null) {
    messages.value = [];
  }
}, { immediate: true });

watch(() => currentConversation.value, (newConversation) => {
  if (newConversation && newConversation.messages) {
    try {
      nextTick(() => scrollToBottom(true));
    } catch (error) {
      console.error('消息处理错误:', error, newConversation.messages);
    }
  }
}, { deep: true });

const updateLoadingState = () => {
  const isLoading = !!currentConversation.value?.messages?.some((m: Message) => m.isLoading);
  if (!isLoading) return;
  const elapsed = Date.now() - loadingStartTime.value;
  if (elapsed < 10000) {
    loadingStage.value = 1;
    loadingStatusText.value = "正在检索相关信息...";
  } else if (elapsed < 30000) {
    loadingStage.value = 2;
    loadingStatusText.value = "正在组织回答内容...";
  } else {
    loadingStage.value = 3;
    loadingStatusText.value = "马上就好，请再等一下...";
  }
  if (Math.floor(elapsed / 10000) > tipIndex.value) {
    tipIndex.value = Math.floor(elapsed / 10000);
    if (minecraftTips && minecraftTips.length > 0) {
      const index = tipIndex.value % minecraftTips.length;
      currentLoadingTip.value = minecraftTips[index];
      console.log(`更新小贴士: ${currentLoadingTip.value} (索引: ${index})`);
    }
  }
};

const scrollToBottom = (withDelay = false) => {
  const doScroll = () => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  };
  doScroll();
  if (withDelay) {
    setTimeout(doScroll, 100);
    setTimeout(doScroll, 300);
    setTimeout(doScroll, 500);
  }
};

// 自动调整textarea高度的函数
const autoResizeTextarea = () => {
  if (inputField.value) {
    // 临时设置为auto以获取实际内容高度
    const originalHeight = inputField.value.style.height;
    inputField.value.style.height = 'auto';

    // 计算所需高度
    const scrollHeight = inputField.value.scrollHeight;
    const minHeight = 38;
    const maxHeight = 80;

    // 设置最终高度
    const finalHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));
    inputField.value.style.height = finalHeight + 'px';

    // 确保样式被应用
    inputField.value.style.boxSizing = 'border-box';
  }
};

const sendMessage = async () => {
  const text = userInput.value.trim();
  const isLoading = !!currentConversation.value?.messages?.some((m: Message) => m.isLoading);
  if (!text || isLoading) return;
  userInput.value = '';
  autoResizeTextarea();
  loadingStartTime.value = Date.now();
  tipIndex.value = 0;
  if (minecraftTips && minecraftTips.length > 0) {
    currentLoadingTip.value = minecraftTips[0];
  } else {
    currentLoadingTip.value = "Minecraft 是一款关于方块与冒险的游戏。";
  }
  const loadingInterval = setInterval(updateLoadingState, 1000);

  // --- 新增: 用于存储 message_complete 数据 ---
  let finalMessageData: { type?: string; item_name?: string } | null = null;
  // --- 结束新增 ---

  try {
    if (isConversationCraftingMode.value) {
      const conv = currentConversation.value!;
      const ctx = store.state.currentCraftingContext; // 从 store 获取上下文

      // placeholder user message
      const userMsg: Message = { id: `${Date.now()}`, role: 'user', type: 'TEXT', content: text, created_at: new Date().toISOString() };
      store.commit('addMessagesToConversation', { conversationId: conv.id, messages: [userMsg] });
      // 添加加载占位助手消息
      store.commit('startLoadingAssistantMessage', { conversationId: conv.id });

      const callbacks = {
        onOpen: () => { },
        // --- Remove onCraftingContext callback ---
        // onCraftingContext: (newCtx: any) => {
        //   // 注意：这里不再替换消息，只更新上下文和触发分屏
        //   // 消息的最终类型由 onMessageComplete 决定
        //   store.commit('updateCraftingContext', { conversationId: conv.id, context: newCtx });
        //   // 触发分屏展示合成树 (如果需要)
        //   if (newCtx?.sharedTree) {
        //      emit('trigger-crafting-split', { craftingData: newCtx.sharedTree, targetItem: newCtx.targetItem }); // 传递更完整的上下文信息
        //   }
        // },
        // --- End removed callback ---
        onMessageChunk: (chunk: string) => {
          store.commit('appendLoadingMessageChunk', { conversationId: conv.id, chunk });
        },
        // --- 新增: 处理 message_complete ---
        onMessageComplete: (data: { type: string; item_name?: string }) => {
          finalMessageData = { type: data.type, item_name: data.item_name };
        },
        // --- 结束新增 ---
        onEnd: () => {
          // --- 修改: 传递 finalMessageData ---
          store.commit('finalizeLoadingMessage', {
            conversationId: conv.id,
            finalType: finalMessageData?.type || 'TEXT', // 默认 TEXT
            finalItemName: finalMessageData?.item_name
          });
          finalMessageData = null; // 重置
          // --- 结束修改 ---
        },
        onError: (msg: string) => {
          console.error('SSE Error:', msg);
          // --- 修改: 处理错误，传递错误信息 ---
          store.commit('finalizeLoadingMessageWithError', {
             conversationId: conv.id,
             errorMessage: msg || '处理时发生未知错误'
          });
          finalMessageData = null; // 重置
          // --- 结束修改 ---
        }
      };

      // 确保从 store 获取最新的 token
      const token = store.state.token;
      if (!token) {
         console.error("用户未登录或 Token 无效");
         store.commit('finalizeLoadingMessageWithError', {
             conversationId: conv.id,
             errorMessage: '用户凭证无效，请重新登录'
         });
         clearInterval(loadingInterval);
         return;
      }

      if (!ctx) {
        // 首次查询 (创建新对话)
        // 注意：startNewConversationSSE 也需要 onMessageComplete 回调
        startNewConversationSSE({ message: text, type: 'SYNTHETIC_NAVIGATION', token: token, callbacks });
      } else {
        // 后续查询 (添加到现有对话)
        addMessageToConversationSSE({ conversationId: String(conv.id), message: text, token: token, callbacks });
      }
    } else {
      // 普通对话模式
      const token = store.state.token;
       if (!token) {
         console.error("用户未登录或 Token 无效");
         // 可以添加错误提示
         clearInterval(loadingInterval);
         return;
      }

      if (props.conversationId && !isTempId(props.conversationId)) {
        await store.dispatch('addMessageToConversation', {
          conversationId: props.conversationId,
          message: text
        });
      } else {
        // 检查是否有预设的对话模式
        const nextMode = store.state.nextConversationMode;
        if (nextMode) {
          // 有预设模式，使用预设模式创建新对话
          store.commit('setNextConversationMode', null); // 清除预设模式
          await store.dispatch('createNewConversation', { message: text, type: nextMode });
        } else {
          // 没有预设模式，使用当前对话的类型或默认为普通对话
          const type = currentConversation.value?.type || 'NORMAL';
          await store.dispatch('createNewConversation', { message: text, type });
        }
      }
    }
    nextTick(() => scrollToBottom(true));
  } catch (error) {
    console.error('发送消息失败:', error);
  } finally {
    clearInterval(loadingInterval);
    nextTick(() => {
      if (inputField.value) {
        inputField.value.focus();
      }
      scrollToBottom(true);
    });
  }
};

const logout = () => {
  store.dispatch('logout');
  router.push('/login');
};

const cancelStream = () => {
  store.dispatch('cancelStream');
};

watch(messages, () => {
  nextTick(() => {
    scrollToBottom(true);
  });
});

watch(() => currentConversation.value?.messages, () => {
  nextTick(() => scrollToBottom(true));
}, { deep: true });

// 监听对话模式变化，重新调整输入框
watch(() => isConversationCraftingMode.value, () => {
  nextTick(() => {
    setTimeout(() => {
      autoResizeTextarea();
    }, 50);
  });
});

const isLoadingResponse = computed(() => !!currentConversation.value?.messages?.some((message: Message) => message.isLoading));

onMounted(() => {
  if (inputField.value) {
    inputField.value.focus();
  }
  if (messagesContainer.value) {
    const observer = new MutationObserver(() => {
      scrollToBottom();
    });
    observer.observe(messagesContainer.value, {
      childList: true,
      subtree: true,
      characterData: true
    });
  }

  // 确保DOM完全渲染后初始化输入框
  nextTick(() => {
    scrollToBottom(true);
    // 多次延迟调用确保对话模式已确定
    setTimeout(() => {
      autoResizeTextarea();
    }, 50);
    setTimeout(() => {
      autoResizeTextarea();
    }, 200);
    setTimeout(() => {
      autoResizeTextarea();
    }, 500);
  });
});

function onExpandSplit(msg: any) {
  console.log('[ChatInterface] onExpandSplit called with:', msg);

  // 处理不同格式的消息，标准化后向上传递
  if (msg) {
    // 情况1: 消息包含craftingData属性（来自CraftingCard的点击）
    if (msg.craftingData) {
      console.log('[ChatInterface] Forwarding message with craftingData');
      emit('trigger-crafting-split', msg);
      return;
    }

    // 情况2: 消息本身就是合成树节点（某些legacy代码可能传递这种格式）
    if (msg.itemName && msg.recipe) {
      console.log('[ChatInterface] Forwarding message that appears to be a crafting tree');
      emit('trigger-crafting-split', { craftingData: msg });
      return;
    }
  }

  // 情况3: 没有收到有效消息，查找最近的合成消息
  const messages = currentConversation.value?.messages || [];
  const lastCraftingMsg = [...messages].reverse().find(m => m.type === 'CRAFTING' && m.craftingData);

  if (lastCraftingMsg) {
    console.log('[ChatInterface] Using latest crafting message:', lastCraftingMsg.id);
    emit('trigger-crafting-split', lastCraftingMsg);
  } else {
    console.log('[ChatInterface] No crafting message found to trigger split pane');
  }
}

// 自动分屏：仅在AI新生成合成树卡片消息时自动触发分屏（不包括重新加载的历史消息）
watch(
  () => currentConversation.value?.messages,
  (messages, oldMessages) => {
    if (
      !props.isSplitPane &&
      messages &&
      messages.length > 0
    ) {
      const lastMsg = messages[messages.length - 1];
      if (lastMsg && lastMsg.type === 'CRAFTING' && lastMsg.craftingData && !lastMsg.isLoading) {
        // 判断是否为新消息：
        // 1. 如果没有旧消息（新建对话），则认为是新消息
        // 2. 如果有旧消息，则检查消息数量是否增加
        const isNewMessage = !oldMessages || messages.length > oldMessages.length;

        if (isNewMessage) {
          console.log('[ChatInterface] Auto-detecting NEW crafting message, triggering split:', lastMsg.id);
          emit('trigger-crafting-split', lastMsg);
        } else {
          console.log('[ChatInterface] Detected existing crafting message, not auto-triggering split:', lastMsg.id);
        }
      }
    }
  },
  { deep: true }
);
</script>

<style scoped lang="scss">
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background-color: rgba(0, 0, 0, 0.7);
  border: 2px solid #5e5e5e;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.chat-header {
  background-color: #2c2c2c;
  padding: 15px;
  border-bottom: 2px solid #5e5e5e;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  /* 三栏布局：左、中、右 */
  align-items: center;
  position: relative;
}

.header-left {
  /* 左侧空白区域，用于保持平衡 */
  width: 100%;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  /* 居中对齐 */
}

.chat-header h1 {
  margin: 0;
  font-size: 24px;
  color: #55ff55;
  text-shadow: 2px 2px #000;
  font-family: 'Minecraft', monospace;
}

.feedback-button {
  margin-left: 15px;
  background-color: rgba(85, 255, 85, 0.2);
  border: 1px solid #55ff55;
  color: #55ff55;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.feedback-button:hover {
  background-color: rgba(85, 255, 85, 0.3);
  transform: translateY(-2px);
}

.feedback-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  position: relative;
  margin-right: 6px;
}

/* 创建一个简单的反馈图标 */
.feedback-icon:before {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #55ff55;
  border-radius: 50%;
  left: 0;
  top: 0;
}

.feedback-icon:after {
  content: '';
  position: absolute;
  width: 6px;
  height: 2px;
  background-color: #55ff55;
  border-radius: 1px;
  left: 5px;
  top: 7px;
  box-shadow: 0 -3px 0 #55ff55, 0 3px 0 #55ff55;
}

.feedback-text {
  font-size: 13px;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: flex-end;
  /* 右对齐 */
  width: 100%;
}

.username {
  color: #3498db;
  /* 蓝色用户名 */
  font-weight: bold;
  font-size: 14px;
}

.logout-button {
  background: none;
  border: none;
  color: #aaaaaa;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s;
}

.logout-button:hover {
  color: #55ff55;
  background-color: rgba(85, 255, 85, 0.1);
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message {
  text-align: center;
  padding: 30px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  margin-top: 40px;
}

/* 更新欢迎消息中的苦力怕图标样式 */
.welcome-message .creeper {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  background-image: url('@/assets/creeper.webp');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* 移除CSS模拟的苦力怕图标样式 */
.welcome-message .creeper:before,
.welcome-message .creeper:after {
  content: none;
}

.welcome-message h2 {
  color: #55ff55;
  margin-bottom: 10px;
  font-family: 'Minecraft', monospace;
}

.welcome-message p {
  color: #aaaaaa;
}

/* 修改消息容器样式 */

/* 调整输入框和按钮的样式 */
.input-bar {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: #181818cc;
  border-radius: 32px;
  box-shadow: 0 2px 16px 0 rgba(85, 255, 85, 0.10);
  padding: 16px 20px;
  width: 90%;
  max-width: 900px;
  min-width: 320px;
  margin: 0 auto 32px auto;
  border: 2px solid #55ff55;
  gap: 0;
  min-height: 70px;
}

.input-row {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  position: relative;
  min-height: 38px;
}

.input-affix-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  align-items: stretch;
  margin-right: 10px;
}

.input-mode-affix {
  position: absolute;
  left: 18px;
  top: 8px;
  display: flex;
  align-items: center;
  color: #2196f3;
  font-size: 16px;
  font-family: 'Minecraft', sans-serif;
  font-weight: 600;
  z-index: 1;
  pointer-events: none;
  line-height: 1.4;
  user-select: none;
  height: 22px;
}

.mode-tag-text {
  font-size: 16px;
  font-family: 'Minecraft', sans-serif;
  line-height: 1.4;
}

.user-input {
  flex: 1;
  min-height: 38px;
  max-height: 80px;
  font-size: 16px;
  padding: 8px 18px 8px 18px;
  /* 合成模式下的padding-left由:style控制 */
  line-height: 1.4;
  border: none;
  outline: none;
  background: transparent;
  color: #fff;
  font-family: 'Minecraft', sans-serif;
  margin: 0;
  box-shadow: none;
  overflow-y: auto;
  resize: none;
  width: 100%;
  box-sizing: border-box;
}

/* 重置placeholder样式，使用简单的方法 */
.user-input::placeholder {
  color: #aaaaaa;
  opacity: 0.8;
}

.send-button {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: #55ff55;
  border: 2px solid #388838;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 4px 0 rgba(85, 255, 85, 0.10);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button:not(:disabled):hover {
  background-color: #7dff7d;
  border-color: #55ff55;
  transform: translateY(-2px);
}

.send-button:not(:disabled):active {
  transform: translateY(0);
}

.send-button .minecraft-icon.arrow {
  width: 20px;
  height: 20px;
  position: relative;
  display: inline-block;
}

.send-button .minecraft-icon.arrow:before {
  content: '';
  position: absolute;
  width: 2px;
  height: 14px;
  background-color: #fff;
  left: 50%;
  top: 3px;
  transform: translateX(-50%);
  border-radius: 1px;
}

.send-button .minecraft-icon.arrow:after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border-top: 2px solid #fff;
  border-right: 2px solid #fff;
  left: 5px;
  top: 5px;
  transform: rotate(-45deg);
  border-radius: 1px;
}

/* 合成树按钮样式 */
.tree-button {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: #55ff55;
  border: 2px solid #388838;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 4px 0 rgba(85, 255, 85, 0.10);
  margin-right: 8px;
}

.tree-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tree-button:not(:disabled):hover {
  background-color: #7dff7d;
  border-color: #55ff55;
  transform: translateY(-2px);
}

.tree-button:not(:disabled):active {
  transform: translateY(0);
}

.tree-button .minecraft-icon.crafting-table {
  width: 20px;
  height: 20px;
  position: relative;
  display: inline-block;
}

/* Minecraft风格工作台图标 */
.tree-button .minecraft-icon.crafting-table {
  position: relative;
  width: 20px;
  height: 20px;
}

/* 工作台基础形状 - 符合绿色主题 */
.tree-button .minecraft-icon.crafting-table:before {
  content: '';
  position: absolute;
  width: 18px;
  height: 18px;
  top: 1px;
  left: 1px;
  background-color: #2b8a2b;
  /* 绿色主题底色 */
  border: 1px solid rgba(0, 0, 0, 0.4);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2);
  border-radius: 1px;
  /* 微妙的纹理效果 */
  background-image:
    linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 50%,
      rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.1) 75%, transparent 75%, transparent);
  background-size: 4px 4px;
}

/* 工作台顶部的3x3网格 - 符合绿色主题 */
.tree-button .minecraft-icon.crafting-table:after {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  top: 3px;
  left: 3px;
  background-color: #55ff55;
  /* 匹配按钮背景色 */
  opacity: 0.8;
  border-radius: 1px;
  /* 创建3x3网格 */
  background-image:
    linear-gradient(to right,
      transparent 32%, rgba(255, 255, 255, 0.9) 32%,
      rgba(255, 255, 255, 0.9) 34%, transparent 34%,
      transparent 65%, rgba(255, 255, 255, 0.9) 65%,
      rgba(255, 255, 255, 0.9) 67%, transparent 67%),
    linear-gradient(to bottom,
      transparent 32%, rgba(255, 255, 255, 0.9) 32%,
      rgba(255, 255, 255, 0.9) 34%, transparent 34%,
      transparent 65%, rgba(255, 255, 255, 0.9) 65%,
      rgba(255, 255, 255, 0.9) 67%, transparent 67%);
  box-shadow:
    /* 在中间添加一个白色亮点 */
    inset 6px 6px 0 1px rgba(255, 255, 255, 0.9),
    /* 添加边框阴影效果 */
    0 0 0 1px rgba(0, 0, 0, 0.3);
}

/* 加载状态样式 */
.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  margin: 0 4px;
  background-color: #ffffff;
  border-radius: 50%;
  display: inline-block;
  animation: loading-dot 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-dot {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

.messages-container::-webkit-scrollbar-thumb {
  background-color: #5e5e5e;
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background-color: #7e7e7e;
}

/* Minecraft风格加载动画样式 */
.minecraft-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  margin-bottom: 10px;
}

.cube-container {
  position: relative;
  width: 30px;
  height: 30px;
  perspective: 100px;
}

.cube {
  position: absolute;
  width: 30px;
  height: 30px;
  transform-style: preserve-3d;
  animation: rotateCube 1.5s infinite linear;
}

.face {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #55ff55;
  border: 1px solid #000;
}

.face.front {
  transform: translateZ(15px);
}

.face.back {
  transform: rotateY(180deg) translateZ(15px);
}

.face.right {
  transform: rotateY(90deg) translateZ(15px);
}

.face.left {
  transform: rotateY(-90deg) translateZ(15px);
}

.face.top {
  transform: rotateX(90deg) translateZ(15px);
}

.face.bottom {
  transform: rotateX(-90deg) translateZ(15px);
}

@keyframes rotateCube {
  0% {
    transform: rotateX(0deg) rotateY(0deg);
  }

  100% {
    transform: rotateX(360deg) rotateY(360deg);
  }
}

.loading-status {
  text-align: center;
  font-size: 14px;
  color: #aaaaaa;
  margin-bottom: 10px;
}

.minecraft-tip {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  padding: 10px;
  color: #ffffff;
  font-size: 14px;
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.tip-icon {
  width: 16px;
  height: 16px;
  background-color: #55ff55;
  border-radius: 50%;
  margin-right: 5px;
}

.tip-content {
  font-size: 12px;
  color: #aaaaaa;
}

/* 取消按钮样式 */
.cancel-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.cancel-button {
  background-color: rgba(255, 85, 85, 0.2);
  border: 1px solid #ff5555;
  color: #ff5555;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: rgba(255, 85, 85, 0.3);
  transform: translateY(-2px);
}

.cancel-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  position: relative;
  margin-right: 6px;
}

/* 创建一个简单的X图标 */
.cancel-icon:before,
.cancel-icon:after {
  content: '';
  position: absolute;
  width: 16px;
  height: 2px;
  background-color: #ff5555;
  top: 7px;
  left: 0;
}

.cancel-icon:before {
  transform: rotate(45deg);
}

.cancel-icon:after {
  transform: rotate(-45deg);
}

/* 错误状态样式 */
.message-content.error {
  background-color: rgba(255, 85, 85, 0.2);
  border: 1px solid #ff5555;
}

.error-text {
  color: #ff5555;
}

/* 来源信息样式已移除 */

/* Markdown内容样式 */
:deep(.assistant .message-text) {

  /* 标题样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-top: 16px;
    margin-bottom: 8px;
    color: #55ff55;
    font-family: 'Minecraft', monospace;
    line-height: 1.2;
  }

  h1 {
    font-size: 1.8em;
  }

  h2 {
    font-size: 1.5em;
  }

  h3 {
    font-size: 1.3em;
  }

  h4 {
    font-size: 1.2em;
  }

  /* 段落样式 */
  p {
    margin-bottom: 10px;
  }

  /* 列表样式 */
  ul,
  ol {
    margin-top: 8px;
    margin-bottom: 8px;
    padding-left: 20px;
  }

  li {
    margin-bottom: 4px;
  }

  /* 代码块样式增强 */
  pre {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    overflow-x: auto;
    font-family: monospace;
    white-space: pre;
    /* 保持原始格式，保留空格和换行 */
    tab-size: 4;
    /* 设置制表符宽度 */
  }

  code {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
    white-space: pre-wrap;
    /* 允许代码换行但保持空格 */
    word-break: keep-all;
    /* 避免单词被打断 */
  }

  /* 对于合成表等特殊代码块，保持格式 */
  pre code {
    display: block;
    white-space: pre;
    /* 保持原始格式和空格 */
    background: transparent;
    /* 移除内部code的背景色 */
    padding: 0;
    /* 移除内部padding */
    font-family: 'Minecraft', monospace;
    /* 使用Minecraft字体 */
    line-height: 1.4;
    /* 适当行高 */
    color: #eeeeee;
    /* 更亮的文字颜色 */
  }

  /* 引用样式 */
  blockquote {
    border-left: 4px solid #55aa55;
    padding-left: 10px;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
    color: #bbbbbb;
  }

  /* 表格样式 */
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
  }

  th,
  td {
    border: 1px solid #55aa55;
    padding: 6px 10px;
  }

  th {
    background-color: rgba(85, 170, 85, 0.2);
    text-align: left;
  }

  /* 水平线样式 */
  hr {
    border: none;
    border-top: 2px solid #55aa55;
    margin: 15px 0;
  }

  /* 链接样式 */
  a {
    color: #55ffff;
    text-decoration: none;
    border-bottom: 1px dotted #55ffff;
  }

  a:hover {
    color: #aaffff;
    border-bottom: 1px solid #aaffff;
  }
}
</style>